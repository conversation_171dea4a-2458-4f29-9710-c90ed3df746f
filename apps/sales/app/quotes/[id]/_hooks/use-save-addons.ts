"use client";

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

interface AddonItem {
  id: string;
  categoryId: string;
  productServiceId: string;
  productServiceName: string;
  startDate: string;
  endDate: string;
  price: number;
  quantity: number;
  total: number;
  // API-provided product identifiers for proper inventory integration
  productId?: string;
  productVariantId?: string;
}

interface SaveAddonsParams {
  addons: AddonItem[];
  cartId: string;
}

interface SaveAddonsResult {
  success: boolean;
  savedCount: number;
  errors: string[];
}

export function useSaveAddons() {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: async ({
      addons,
      cartId,
    }: SaveAddonsParams): Promise<SaveAddonsResult> => {
      try {
        // Map addons to cart items format
        const items = addons.map((addon) => ({
          // Use API-provided product identifiers if available, fallback to legacy format
          variant_id: addon.productVariantId,
          product_id: addon.productId,
          quantity: addon.quantity,
          unit_price: addon.price * 100, // Convert to pence/cents
          title: addon.productServiceName,
          metadata: {
            category_id: addon.categoryId,
            product_service_id: addon.productServiceId,
            cost_price: Math.round(addon.price * 0.7 * 100), // Assume 30% markup, convert to pence
            start_date: addon.startDate,
            end_date: addon.endDate,
            type: "addon",
          },
        }));

        console.log("🛒 Calling wrapper API to save addons:", {
          cartId,
          items,
        });

        // Call the wrapper API
        const response = await fetch("/api/room-addon-add/save", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            cartId,
            items,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log("🎉 Wrapper API call successful:", result);

        return {
          success: true,
          savedCount: addons.length,
          errors: [],
        };
      } catch (error) {
        console.error("Failed to save addons via wrapper API:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        return {
          success: false,
          savedCount: 0,
          errors: [`Failed to save addons: ${errorMessage}`],
        };
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(`Successfully saved ${result.savedCount} addon${result.savedCount !== 1 ? "s" : ""} to cart`);
      } else {
        result.errors.forEach((error) => toast.error(error));
      }
    },
    onError: (error) => {
      console.error("Mutation error:", error);
      toast.error("Failed to save addons");
    },
  });

  const saveAddons = async (params: SaveAddonsParams) => {
    setIsLoading(true);
    try {
      const result = await mutation.mutateAsync(params);
      return result;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    saveAddons,
    isLoading: isLoading || mutation.isPending,
    error: mutation.error,
  };
}
