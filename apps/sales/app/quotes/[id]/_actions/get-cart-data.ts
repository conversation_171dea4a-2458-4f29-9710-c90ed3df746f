"use server";

import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";
import { RoomBlock } from "../_components/hotel-booking-calendar";

// Addon interface to match the quotation form structure
interface AddonItem {
  id: string;
  categoryId: string;
  productServiceId: string;
  productServiceName: string;
  startDate: string;
  endDate: string;
  price: number;
  quantity: number;
  total: number;
  // API-provided product identifiers for proper inventory integration
  productId?: string;
  productVariantId?: string;
}

interface CartItem {
  id: string;
  variant_id: string;
  title: string;
  quantity: number;
  unit_price: number;
  product_id?: string;
  product_title?: string;
  product_description?: string;
  product_subtitle?: string;
  product_type?: string;
  product_type_id?: string;
  product_collection?: string;
  product_handle?: string;
  raw_compare_at_unit_price?: number;
  metadata?: {
    room_id?: string;
    hotel_id?: string;
    hotel_name?: string;
    start_date?: string;
    end_date?: string;
    room_config_id?: string;
    room_config_name?: string;
    number_of_rooms?: number;
    occupancy_type_id?: string;
    occupancy_type_name?: string;
    meal_plan_id?: string;
    meal_plan_name?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

// Transform cart item to RoomBlock
function transformCartItemToRoomBlock(item: CartItem): RoomBlock | null {
  const metadata = item.metadata;
  
  if (!metadata || !metadata.room_id || !metadata.start_date || !metadata.end_date) {
    console.warn("Cart item missing required room booking metadata:", item);
    return null;
  }

  // Calculate nights between check-in and check-out
  const checkInDate = new Date(metadata.start_date);
  const checkOutDate = new Date(metadata.end_date);
  const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));

  // Calculate rate per night
  const rate = nights > 0 ? item.unit_price / nights : item.unit_price;

  return {
    id: item.id,
    room_id: metadata.room_id,
    room_name: item.product_title || metadata.room_config_name || "Unknown Room",
    product_id: metadata.room_config_id || item.product_id || "unknown",
    config_name: metadata.room_config_name || "Unknown Config",
    start_date: metadata.start_date,
    end_date: metadata.end_date,
    nights: nights,
    rate: rate,
    total: item.unit_price,
    occupancy_type_id: metadata.occupancy_type_id,
    meal_plan_id: metadata.meal_plan_id,
  };
}

// Transform cart item to AddonItem
function transformCartItemToAddon(item: CartItem): AddonItem | null {
  const metadata = item.metadata;
  console.log({item})

  // Filter for addon items - check for legacy hardcoded product_id or addon type in metadata
  if (!metadata || (item.product_id !== "product_add_ons_main" && metadata.type !== "addon")) {
    return null;
  }

  return {
    id: item.id,
    categoryId: metadata.category_id,
    productServiceId: metadata.product_service_id,
    productServiceName: item.title || "Unknown Service",
    startDate: metadata.start_date,
    endDate: metadata.end_date,
    price: item.unit_price / 100, // Convert from pence/cents to dollars
    quantity: item.quantity,
    total: (item.unit_price * item.quantity) / 100, // Convert total from pence/cents to dollars
    // Preserve the actual product identifiers from the cart
    productId: item.product_id,
    productVariantId: item.variant_id,
  };
}

interface CartDataResult {
  roomBlocks: RoomBlock[];
  addons: AddonItem[];
  cartDetails: any; // Complete cart object from inventory API
}

export async function getCartData(cartId: string | null): Promise<CartDataResult> {
  if (!cartId) {
    return {
      roomBlocks: [],
      addons: [],
      cartDetails: null,
    };
  }

  try {
    const { tenantId, userId } = await getServerSession();
    
    if (!userId || !tenantId) {
      console.error("❌ Unauthorized: No user or tenant ID");
      throw new Error("Unauthorized");
    }
    const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
      where: {
        tenantId: tenantId,
      },
      select: {
        apiUrl: true,
        token: true,
        isActive: true,
        verificationStatus: true,
      },
    });

    // Check if inventory is configured and active
    if (!inventoryConfig || !inventoryConfig.apiUrl || !inventoryConfig.token) {
      console.error("❌ Inventory system not configured for tenant:", tenantId);
      throw new Error("Inventory system not configured for this tenant");
    }

    if (!inventoryConfig.isActive) {
      console.error("❌ Inventory system is not active for tenant:", tenantId);
      throw new Error("Inventory system is not active");
    }

    const inventoryAPI = new FlinkkInventoryAPI({
      apiUrl: inventoryConfig.apiUrl,
      token: inventoryConfig.token,
    });

    // Get cart data from inventory API
    const cartResponse = await inventoryAPI.getCart(cartId);

    // Transform cart items to RoomBlocks and Addons
    const roomBlocks: RoomBlock[] = [];
    const addons: AddonItem[] = [];

    if (cartResponse.cart?.items) {
      for (const item of cartResponse.cart.items) {
        // Try to transform as room block
        const roomBlock = transformCartItemToRoomBlock(item);
        if (roomBlock) {
          roomBlocks.push(roomBlock);
          continue;
        }

        // Try to transform as addon
        const addon = transformCartItemToAddon(item);
        if (addon) {
          addons.push(addon);
        }
      }
    }
    // Ensure cart details are properly serialized with expanded items
    const cartDetails = {
      ...cartResponse.cart,
      // Explicitly serialize items to ensure they're not truncated
      items: cartResponse.cart.items?.map((item: any) => ({
        id: item.id,
        thumbnail: item.thumbnail,
        variant_id: item.variant_id,
        product_id: item.product_id,
        product_type_id: item.product_type_id,
        product_title: item.product_title,
        product_description: item.product_description,
        product_subtitle: item.product_subtitle,
        product_type: item.product_type,
        product_collection: item.product_collection,
        product_handle: item.product_handle,
        variant_sku: item.variant_sku,
        variant_barcode: item.variant_barcode,
        variant_title: item.variant_title,
        requires_shipping: item.requires_shipping,
        metadata: item.metadata,
        created_at: item.created_at,
        updated_at: item.updated_at,
        title: item.title,
        quantity: item.quantity,
        unit_price: item.unit_price,
        compare_at_unit_price: item.compare_at_unit_price,
        is_tax_inclusive: item.is_tax_inclusive,
        tax_lines: item.tax_lines,
        adjustments: item.adjustments,
      })) || [],
    };

    return {
      roomBlocks,
      addons,
      cartDetails,
    };

  } catch (error) {
    console.error("❌ Error getting cart data:", error);
    
    // Return empty result instead of throwing to prevent page crashes
    // The UI can handle empty state gracefully
    return {
      roomBlocks: [],
      addons: [],
      cartDetails: null,
    };
  }
}
