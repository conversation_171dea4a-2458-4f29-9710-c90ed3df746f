import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";

export const dynamic = "force-dynamic";

// POST /api/dynamic-select/addon-categories - Get addon categories from inventory service
export async function POST(req: NextRequest) {
  try {
    const { tenantId } = await getToken({ req });

    // Get inventory configuration for the tenant
    const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
      where: {
        tenantId,
      },
      select: {
        apiUrl: true,
        token: true,
        isActive: true,
        verificationStatus: true,
      },
    });

    // Check if inventory is configured and active
    if (!inventoryConfig || !inventoryConfig.apiUrl || !inventoryConfig.token) {
      return NextResponse.json([]);
    }

    // Create inventory API instance
    const inventoryAPI = new FlinkkInventoryAPI({
      apiUrl: inventoryConfig.apiUrl,
      token: inventoryConfig.token,
    });

    // Get addon categories using the API
    const data = await inventoryAPI.getAddonCategories({
      is_active: true,
      limit: 50,
      sort_by: "name",
      sort_order: "asc",
    });

    // Transform categories to DynamicSelect option format
    const options = data?.result.map((category: any) => ({
      value: category.id || category._id,
      label: category.name || category.title,
      description: category.description,
    }));

    return NextResponse.json(options);
  } catch (error) {
    console.error(
      "Error fetching addon categories from inventory service:",
      error,
    );
    return NextResponse.json([]);
  }
}
